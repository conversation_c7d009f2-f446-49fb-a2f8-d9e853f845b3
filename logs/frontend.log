当前工作目录: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend
启动语义化URL服务器在端口 8083...
服务器地址: http://localhost:8083

支持的语义化URL模式:
- /authors/ -> authors.html
- /authors/{slug}/ -> author.html
- /authors/{slug}/quotes/ -> author.html
- /categories/ -> categories.html
- /categories/{slug}/ -> category.html
- /categories/{slug}/quotes/ -> category.html
- /sources/ -> sources.html
- /sources/{slug}/ -> source.html
- /quotes/ -> quotes.html
- /quotes/{id}/ -> quote.html
- /search/ -> search.html

按 Ctrl+C 停止服务器
🔍 收到GET请求: /
🔍 解析路径: /
❌ 未匹配到任何语义化URL模式: /
[127.0.0.1] "GET / HTTP/1.1" 200 -
🔍 收到GET请求: /
🔍 解析路径: /
❌ 未匹配到任何语义化URL模式: /
[127.0.0.1] "GET / HTTP/1.1" 200 -
🔍 收到GET请求: /
🔍 解析路径: /
❌ 未匹配到任何语义化URL模式: /
[127.0.0.1] "GET / HTTP/1.1" 304 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/config.js?v=20250623
🔍 解析路径: /js/config.js
❌ 未匹配到任何语义化URL模式: /js/config.js
✅ 静态文件请求: /js/config.js
[127.0.0.1] "GET /js/config.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/url-handler.js?v=20250623
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/entity-id-mapper.js?v=20250623
🔍 解析路径: /js/entity-id-mapper.js
❌ 未匹配到任何语义化URL模式: /js/entity-id-mapper.js
✅ 静态文件请求: /js/entity-id-mapper.js
[127.0.0.1] "GET /js/entity-id-mapper.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250623
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/navigation-state.js?v=20250627
🔍 解析路径: /js/navigation-state.js
❌ 未匹配到任何语义化URL模式: /js/navigation-state.js
✅ 静态文件请求: /js/navigation-state.js
[127.0.0.1] "GET /js/navigation-state.js?v=20250627 HTTP/1.1" 304 -
🔍 收到GET请求: /components/navigation.html
🔍 解析路径: /components/navigation.html
❌ 未匹配到任何语义化URL模式: /components/navigation.html
✅ 静态文件请求: /components/navigation.html
[127.0.0.1] "GET /components/navigation.html HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/navigation.js
🔍 解析路径: /js/components/navigation.js
❌ 未匹配到任何语义化URL模式: /js/components/navigation.js
✅ 静态文件请求: /js/components/navigation.js
[127.0.0.1] "GET /js/components/navigation.js HTTP/1.1" 304 -
🔍 收到GET请求: /categories/
🔍 解析路径: /categories/
✅ 匹配到语义化URL模式: ^/categories/$ -> categories.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/categories.html
   ✅ 重写路径为: /categories.html
未匹配的路径，可能需要添加新的URL模式: /categories/
重定向到 categories.html
[127.0.0.1] "GET /categories/ HTTP/1.1" 304 -
🔍 收到GET请求: /css/performance-optimizations.css
🔍 解析路径: /css/performance-optimizations.css
❌ 未匹配到任何语义化URL模式: /css/performance-optimizations.css
✅ 静态文件请求: /css/performance-optimizations.css
[127.0.0.1] "GET /css/performance-optimizations.css HTTP/1.1" 304 -
🔍 收到GET请求: /js/performance-monitor.js?v=20250627
🔍 解析路径: /js/performance-monitor.js
❌ 未匹配到任何语义化URL模式: /js/performance-monitor.js
✅ 静态文件请求: /js/performance-monitor.js
[127.0.0.1] "GET /js/performance-monitor.js?v=20250627 HTTP/1.1" 304 -
🔍 收到GET请求: /js/config.js?v=20250626
🔍 解析路径: /js/config.js
❌ 未匹配到任何语义化URL模式: /js/config.js
✅ 静态文件请求: /js/config.js
[127.0.0.1] "GET /js/config.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/pages/categories.js?v=20250626
🔍 解析路径: /js/pages/categories.js
❌ 未匹配到任何语义化URL模式: /js/pages/categories.js
✅ 静态文件请求: /js/pages/categories.js
[127.0.0.1] "GET /js/pages/categories.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/test-breadcrumb.js?v=20250627
🔍 解析路径: /js/test-breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/test-breadcrumb.js
✅ 静态文件请求: /js/test-breadcrumb.js
[127.0.0.1] "GET /js/test-breadcrumb.js?v=20250627 HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /authors/
🔍 解析路径: /authors/
✅ 匹配到语义化URL模式: ^/authors/$ -> authors.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/authors.html
   ✅ 重写路径为: /authors.html
未匹配的路径，可能需要添加新的URL模式: /authors/
重定向到 authors.html
[127.0.0.1] "GET /authors/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/authors.js?v=20250626
🔍 解析路径: /js/pages/authors.js
❌ 未匹配到任何语义化URL模式: /js/pages/authors.js
✅ 静态文件请求: /js/pages/authors.js
[127.0.0.1] "GET /js/pages/authors.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /sources/
🔍 解析路径: /sources/
✅ 匹配到语义化URL模式: ^/sources/$ -> sources.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/sources.html
   ✅ 重写路径为: /sources.html
未匹配的路径，可能需要添加新的URL模式: /sources/
重定向到 sources.html
[127.0.0.1] "GET /sources/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/sources.js?v=20250626
🔍 解析路径: /js/pages/sources.js
❌ 未匹配到任何语义化URL模式: /js/pages/sources.js
✅ 静态文件请求: /js/pages/sources.js
[127.0.0.1] "GET /js/pages/sources.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /sources/biography/
🔍 解析路径: /sources/biography/
✅ 匹配到语义化URL模式: ^/sources/([a-zA-Z0-9\-_]+)/$ -> source.html
   匹配组: ('biography',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/source.html
   ✅ 重写路径为: /source.html
未匹配的路径，可能需要添加新的URL模式: /sources/biography/
重定向到 source.html
[127.0.0.1] "GET /sources/biography/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/source.js?v=20250626
🔍 解析路径: /js/pages/source.js
❌ 未匹配到任何语义化URL模式: /js/pages/source.js
✅ 静态文件请求: /js/pages/source.js
[127.0.0.1] "GET /js/pages/source.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/entity-id-mapper-production.js?v=20250627
🔍 解析路径: /js/entity-id-mapper-production.js
❌ 未匹配到任何语义化URL模式: /js/entity-id-mapper-production.js
✅ 静态文件请求: /js/entity-id-mapper-production.js
[127.0.0.1] "GET /js/entity-id-mapper-production.js?v=20250627 HTTP/1.1" 304 -
🔍 收到GET请求: /js/entity-id-mapper.js?v=20250627
🔍 解析路径: /js/entity-id-mapper.js
❌ 未匹配到任何语义化URL模式: /js/entity-id-mapper.js
✅ 静态文件请求: /js/entity-id-mapper.js
[127.0.0.1] "GET /js/entity-id-mapper.js?v=20250627 HTTP/1.1" 304 -----------------------------------------
Exception occurred during processing of request from ('127.0.0.1', 56813)
Traceback (most recent call last):
  File "/Users/<USER>/Documents/quotese_0503_0628_V4/frontend/semantic_url_server.py", line 206, in do_GET
    super().do_GET()
    ~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 676, in do_GET
    f = self.send_head()
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 771, in send_head
    self.end_headers()
    ~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/quotese_0503_0628_V4/frontend/semantic_url_server.py", line 216, in end_headers
    super().end_headers()
    ~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 538, in end_headers
    self.flush_headers()
    ~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 542, in flush_headers
    self.wfile.write(b"".join(self._headers_buffer))
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 845, in write
    self._sock.sendall(b)
    ~~~~~~~~~~~~~~~~~~^^^
BrokenPipeError: [Errno 32] Broken pipe

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 318, in _handle_request_noblock
    self.process_request(request, client_address)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 349, in process_request
    self.finish_request(request, client_address)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/quotese_0503_0628_V4/frontend/semantic_url_server.py", line 22, in __init__
    super().__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 672, in __init__
    super().__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 766, in __init__
    self.handle()
    ~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 436, in handle
    self.handle_one_request()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 424, in handle_one_request
    method()
    ~~~~~~^^
  File "/Users/<USER>/Documents/quotese_0503_0628_V4/frontend/semantic_url_server.py", line 209, in do_GET
    self.send_error(500, f"Internal server error: {e}")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 488, in send_error
    self.end_headers()
    ~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/quotese_0503_0628_V4/frontend/semantic_url_server.py", line 216, in end_headers
    super().end_headers()
    ~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 538, in end_headers
    self.flush_headers()
    ~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 542, in flush_headers
    self.wfile.write(b"".join(self._headers_buffer))
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 845, in write
    self._sock.sendall(b)
    ~~~~~~~~~~~~~~~~~~^^^
BrokenPipeError: [Errno 32] Broken pipe
----------------------------------------

🔍 收到GET请求: /js/performance-test.js?v=20250626
🔍 解析路径: /js/performance-test.js
❌ 未匹配到任何语义化URL模式: /js/performance-test.js
✅ 静态文件请求: /js/performance-test.js
[127.0.0.1] "GET /js/performance-test.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/mobile-performance-optimizer.js?v=20250626
🔍 解析路径: /js/mobile-performance-optimizer.js
❌ 未匹配到任何语义化URL模式: /js/mobile-performance-optimizer.js
✅ 静态文件请求: /js/mobile-performance-optimizer.js
[127.0.0.1] "GET /js/mobile-performance-optimizer.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /authors/ursula-k-le-guin/
🔍 解析路径: /authors/ursula-k-le-guin/
✅ 匹配到语义化URL模式: ^/authors/([a-zA-Z0-9\-_]+)/$ -> author.html
   匹配组: ('ursula-k-le-guin',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/author.html
   ✅ 重写路径为: /author.html
未匹配的路径，可能需要添加新的URL模式: /authors/ursula-k-le-guin/
重定向到 author.html
[127.0.0.1] "GET /authors/ursula-k-le-guin/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/author.js?v=20250626
🔍 解析路径: /js/pages/author.js
❌ 未匹配到任何语义化URL模式: /js/pages/author.js
✅ 静态文件请求: /js/pages/author.js
[127.0.0.1] "GET /js/pages/author.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /sources/the-art-of-action-8-ways-to-initiate-activate-forward-momentum-for-positive-impact/
🔍 解析路径: /sources/the-art-of-action-8-ways-to-initiate-activate-forward-momentum-for-positive-impact/
✅ 匹配到语义化URL模式: ^/sources/([a-zA-Z0-9\-_]+)/$ -> source.html
   匹配组: ('the-art-of-action-8-ways-to-initiate-activate-forward-momentum-for-positive-impact',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/source.html
   ✅ 重写路径为: /source.html
未匹配的路径，可能需要添加新的URL模式: /sources/the-art-of-action-8-ways-to-initiate-activate-forward-momentum-for-positive-impact/
重定向到 source.html
[127.0.0.1] "GET /sources/the-art-of-action-8-ways-to-initiate-activate-forward-momentum-for-positive-impact/ HTTP/1.1" 200 -
🔍 收到GET请求: /sources/leaders-watchwords/
🔍 解析路径: /sources/leaders-watchwords/
✅ 匹配到语义化URL模式: ^/sources/([a-zA-Z0-9\-_]+)/$ -> source.html
   匹配组: ('leaders-watchwords',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/source.html
   ✅ 重写路径为: /source.html
未匹配的路径，可能需要添加新的URL模式: /sources/leaders-watchwords/
重定向到 source.html
[127.0.0.1] "GET /sources/leaders-watchwords/ HTTP/1.1" 200 -
🔍 收到GET请求: /sources/life/
🔍 解析路径: /sources/life/
✅ 匹配到语义化URL模式: ^/sources/([a-zA-Z0-9\-_]+)/$ -> source.html
   匹配组: ('life',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/source.html
   ✅ 重写路径为: /source.html
未匹配的路径，可能需要添加新的URL模式: /sources/life/
重定向到 source.html
[127.0.0.1] "GET /sources/life/ HTTP/1.1" 200 -
🔍 收到GET请求: /sources/essay/
🔍 解析路径: /sources/essay/
✅ 匹配到语义化URL模式: ^/sources/([a-zA-Z0-9\-_]+)/$ -> source.html
   匹配组: ('essay',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/source.html
   ✅ 重写路径为: /source.html
未匹配的路径，可能需要添加新的URL模式: /sources/essay/
重定向到 source.html
[127.0.0.1] "GET /sources/essay/ HTTP/1.1" 200 -
🔍 收到GET请求: /sources/love/
🔍 解析路径: /sources/love/
✅ 匹配到语义化URL模式: ^/sources/([a-zA-Z0-9\-_]+)/$ -> source.html
   匹配组: ('love',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/source.html
   ✅ 重写路径为: /source.html
未匹配的路径，可能需要添加新的URL模式: /sources/love/
重定向到 source.html
[127.0.0.1] "GET /sources/love/ HTTP/1.1" 200 -
🔍 收到GET请求: /test-production-api.html
🔍 解析路径: /test-production-api.html
❌ 未匹配到任何语义化URL模式: /test-production-api.html
[127.0.0.1] "GET /test-production-api.html HTTP/1.1" 200 -
🔍 收到GET请求: /js/config.js
🔍 解析路径: /js/config.js
❌ 未匹配到任何语义化URL模式: /js/config.js
✅ 静态文件请求: /js/config.js
[127.0.0.1] "GET /js/config.js HTTP/1.1" 200 -
🔍 收到GET请求: /favicon.ico
🔍 解析路径: /favicon.ico
❌ 未匹配到任何语义化URL模式: /favicon.ico
未匹配的路径，可能需要添加新的URL模式: /favicon.ico
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /favicon.ico HTTP/1.1" 404 -
🔍 收到GET请求: /test-production-api.html
🔍 解析路径: /test-production-api.html
❌ 未匹配到任何语义化URL模式: /test-production-api.html
[127.0.0.1] "GET /test-production-api.html HTTP/1.1" 304 -
🔍 收到GET请求: /test-production-api.html
🔍 解析路径: /test-production-api.html
❌ 未匹配到任何语义化URL模式: /test-production-api.html
[127.0.0.1] "GET /test-production-api.html HTTP/1.1" 304 -
🔍 收到GET请求: /js/config.js
🔍 解析路径: /js/config.js
❌ 未匹配到任何语义化URL模式: /js/config.js
✅ 静态文件请求: /js/config.js
[127.0.0.1] "GET /js/config.js HTTP/1.1" 304 -
🔍 收到GET请求: /test-production-api.html
🔍 解析路径: /test-production-api.html
❌ 未匹配到任何语义化URL模式: /test-production-api.html
[127.0.0.1] "GET /test-production-api.html HTTP/1.1" 200 -
🔍 收到GET请求: /
🔍 解析路径: /
❌ 未匹配到任何语义化URL模式: /
[127.0.0.1] "GET / HTTP/1.1" 200 -
🔍 收到GET请求: /test-production-api.html
🔍 解析路径: /test-production-api.html
❌ 未匹配到任何语义化URL模式: /test-production-api.html
[127.0.0.1] "GET /test-production-api.html HTTP/1.1" 200 -
🔍 收到GET请求: /test-production-compatibility.html
🔍 解析路径: /test-production-compatibility.html
❌ 未匹配到任何语义化URL模式: /test-production-compatibility.html
[127.0.0.1] "GET /test-production-compatibility.html HTTP/1.1" 200 -
🔍 收到GET请求: /js/config.js
🔍 解析路径: /js/config.js
❌ 未匹配到任何语义化URL模式: /js/config.js
✅ 静态文件请求: /js/config.js
[127.0.0.1] "GET /js/config.js HTTP/1.1" 304 -
🔍 收到GET请求: /js/api-client.js
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js HTTP/1.1" 200 -
🔍 收到GET请求: /test-production-compatibility.html
🔍 解析路径: /test-production-compatibility.html
❌ 未匹配到任何语义化URL模式: /test-production-compatibility.html
[127.0.0.1] "GET /test-production-compatibility.html HTTP/1.1" 304 -
🔍 收到GET请求: /
🔍 解析路径: /
❌ 未匹配到任何语义化URL模式: /
[127.0.0.1] "GET / HTTP/1.1" 200 -
处理请求时出错: [Errno 32] Broken pipe
[127.0.0.1] code 500, message Internal server error: [Errno 32] Broken pipe
[127.0.0.1] "GET / HTTP/1.1" 500 -
🔍 收到GET请求: /
🔍 解析路径: /
❌ 未匹配到任何语义化URL模式: /
[127.0.0.1] "GET / HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/config.js?v=20250623
🔍 解析路径: /js/config.js
❌ 未匹配到任何语义化URL模式: /js/config.js
✅ 静态文件请求: /js/config.js
[127.0.0.1] "GET /js/config.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250623
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250623
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250623
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /css/index.css
🔍 解析路径: /css/index.css
❌ 未匹配到任何语义化URL模式: /css/index.css
✅ 静态文件请求: /css/index.css
[127.0.0.1] "GET /css/index.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 304 -
🔍 收到GET请求: /js/url-handler.js?v=20250623
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/entity-id-mapper.js?v=20250623
🔍 解析路径: /js/entity-id-mapper.js
❌ 未匹配到任何语义化URL模式: /js/entity-id-mapper.js
✅ 静态文件请求: /js/entity-id-mapper.js
[127.0.0.1] "GET /js/entity-id-mapper.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/optimized-navigation.js?v=20250623
🔍 解析路径: /js/optimized-navigation.js
❌ 未匹配到任何语义化URL模式: /js/optimized-navigation.js
✅ 静态文件请求: /js/optimized-navigation.js
[127.0.0.1] "GET /js/optimized-navigation.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250623
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250623
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250623
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/pagination.js?v=20250623
🔍 解析路径: /js/components/pagination.js
❌ 未匹配到任何语义化URL模式: /js/components/pagination.js
✅ 静态文件请求: /js/components/pagination.js
[127.0.0.1] "GET /js/components/pagination.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/quote-card.js?v=20250623
🔍 解析路径: /js/components/quote-card.js
❌ 未匹配到任何语义化URL模式: /js/components/quote-card.js
✅ 静态文件请求: /js/components/quote-card.js
[127.0.0.1] "GET /js/components/quote-card.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250623
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/navigation-state.js?v=20250627
🔍 解析路径: /js/navigation-state.js
❌ 未匹配到任何语义化URL模式: /js/navigation-state.js
✅ 静态文件请求: /js/navigation-state.js
[127.0.0.1] "GET /js/navigation-state.js?v=20250627 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250623
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250623
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/index.js?v=20250623
🔍 解析路径: /js/pages/index.js
❌ 未匹配到任何语义化URL模式: /js/pages/index.js
✅ 静态文件请求: /js/pages/index.js
[127.0.0.1] "GET /js/pages/index.js?v=20250623 HTTP/1.1" 200 -
🔍 收到GET请求: /components/navigation.html
🔍 解析路径: /components/navigation.html
❌ 未匹配到任何语义化URL模式: /components/navigation.html
✅ 静态文件请求: /components/navigation.html
[127.0.0.1] "GET /components/navigation.html HTTP/1.1" 200 -
🔍 收到GET请求: /components/footer.html
🔍 解析路径: /components/footer.html
❌ 未匹配到任何语义化URL模式: /components/footer.html
✅ 静态文件请求: /components/footer.html
[127.0.0.1] "GET /components/footer.html HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/navigation.js
🔍 解析路径: /js/components/navigation.js
❌ 未匹配到任何语义化URL模式: /js/components/navigation.js
✅ 静态文件请求: /js/components/navigation.js
[127.0.0.1] "GET /js/components/navigation.js HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/footer.js
🔍 解析路径: /js/components/footer.js
❌ 未匹配到任何语义化URL模式: /js/components/footer.js
✅ 静态文件请求: /js/components/footer.js
[127.0.0.1] "GET /js/components/footer.js HTTP/1.1" 200 -
🔍 收到GET请求: /components/quotes-list.html
🔍 解析路径: /components/quotes-list.html
❌ 未匹配到任何语义化URL模式: /components/quotes-list.html
✅ 静态文件请求: /components/quotes-list.html
[127.0.0.1] "GET /components/quotes-list.html HTTP/1.1" 200 -
🔍 收到GET请求: /components/pagination.html
🔍 解析路径: /components/pagination.html
❌ 未匹配到任何语义化URL模式: /components/pagination.html
✅ 静态文件请求: /components/pagination.html
[127.0.0.1] "GET /components/pagination.html HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/quotes-list.js
🔍 解析路径: /js/components/quotes-list.js
❌ 未匹配到任何语义化URL模式: /js/components/quotes-list.js
✅ 静态文件请求: /js/components/quotes-list.js
[127.0.0.1] "GET /js/components/quotes-list.js HTTP/1.1" 200 -
🔍 收到GET请求: /components/popular-topics.html
🔍 解析路径: /components/popular-topics.html
❌ 未匹配到任何语义化URL模式: /components/popular-topics.html
✅ 静态文件请求: /components/popular-topics.html
[127.0.0.1] "GET /components/popular-topics.html HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/popular-topics.js
🔍 解析路径: /js/components/popular-topics.js
❌ 未匹配到任何语义化URL模式: /js/components/popular-topics.js
✅ 静态文件请求: /js/components/popular-topics.js
[127.0.0.1] "GET /js/components/popular-topics.js HTTP/1.1" 200 -
🔍 收到GET请求: /categories/self/
🔍 解析路径: /categories/self/
✅ 匹配到语义化URL模式: ^/categories/([a-zA-Z0-9\-_]+)/$ -> category.html
   匹配组: ('self',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/category.html
   ✅ 重写路径为: /category.html
未匹配的路径，可能需要添加新的URL模式: /categories/self/
重定向到 category.html
[127.0.0.1] "GET /categories/self/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-error-handler.js?v=20250626
🔍 解析路径: /js/global-error-handler.js
❌ 未匹配到任何语义化URL模式: /js/global-error-handler.js
✅ 静态文件请求: /js/global-error-handler.js
[127.0.0.1] "GET /js/global-error-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-load-monitor.js?v=20250626
🔍 解析路径: /js/page-load-monitor.js
❌ 未匹配到任何语义化URL模式: /js/page-load-monitor.js
✅ 静态文件请求: /js/page-load-monitor.js
[127.0.0.1] "GET /js/page-load-monitor.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/componentloader-init-checker.js?v=20250626
🔍 解析路径: /js/componentloader-init-checker.js
❌ 未匹配到任何语义化URL模式: /js/componentloader-init-checker.js
✅ 静态文件请求: /js/componentloader-init-checker.js
[127.0.0.1] "GET /js/componentloader-init-checker.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/entity-id-mapper-production.js?v=20250627
🔍 解析路径: /js/entity-id-mapper-production.js
❌ 未匹配到任何语义化URL模式: /js/entity-id-mapper-production.js
✅ 静态文件请求: /js/entity-id-mapper-production.js
[127.0.0.1] "GET /js/entity-id-mapper-production.js?v=20250627 HTTP/1.1" 200 -
🔍 收到GET请求: /js/entity-id-mapper.js?v=20250627
🔍 解析路径: /js/entity-id-mapper.js
❌ 未匹配到任何语义化URL模式: /js/entity-id-mapper.js
✅ 静态文件请求: /js/entity-id-mapper.js
[127.0.0.1] "GET /js/entity-id-mapper.js?v=20250627 HTTP/1.1" 200 -
🔍 收到GET请求: /js/optimized-navigation.js?v=20250626
🔍 解析路径: /js/optimized-navigation.js
❌ 未匹配到任何语义化URL模式: /js/optimized-navigation.js
✅ 静态文件请求: /js/optimized-navigation.js
[127.0.0.1] "GET /js/optimized-navigation.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-performance-optimizer.js?v=20250626
🔍 解析路径: /js/mobile-performance-optimizer.js
❌ 未匹配到任何语义化URL模式: /js/mobile-performance-optimizer.js
✅ 静态文件请求: /js/mobile-performance-optimizer.js
[127.0.0.1] "GET /js/mobile-performance-optimizer.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/performance-test.js?v=20250626
🔍 解析路径: /js/performance-test.js
❌ 未匹配到任何语义化URL模式: /js/performance-test.js
✅ 静态文件请求: /js/performance-test.js
[127.0.0.1] "GET /js/performance-test.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug-componentloader.js?v=20250626
🔍 解析路径: /js/debug-componentloader.js
❌ 未匹配到任何语义化URL模式: /js/debug-componentloader.js
✅ 静态文件请求: /js/debug-componentloader.js
[127.0.0.1] "GET /js/debug-componentloader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/pagination.js?v=20250626
🔍 解析路径: /js/components/pagination.js
❌ 未匹配到任何语义化URL模式: /js/components/pagination.js
✅ 静态文件请求: /js/components/pagination.js
[127.0.0.1] "GET /js/components/pagination.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/quote-card.js?v=20250626
🔍 解析路径: /js/components/quote-card.js
❌ 未匹配到任何语义化URL模式: /js/components/quote-card.js
✅ 静态文件请求: /js/components/quote-card.js
[127.0.0.1] "GET /js/components/quote-card.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/category.js?v=20250626
🔍 解析路径: /js/pages/category.js
❌ 未匹配到任何语义化URL模式: /js/pages/category.js
✅ 静态文件请求: /js/pages/category.js
[127.0.0.1] "GET /js/pages/category.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /components/breadcrumb.html
🔍 解析路径: /components/breadcrumb.html
❌ 未匹配到任何语义化URL模式: /components/breadcrumb.html
✅ 静态文件请求: /components/breadcrumb.html
[127.0.0.1] "GET /components/breadcrumb.html HTTP/1.1" 200 -
🔍 收到GET请求: /authors/albert-einstein/
🔍 解析路径: /authors/albert-einstein/
✅ 匹配到语义化URL模式: ^/authors/([a-zA-Z0-9\-_]+)/$ -> author.html
   匹配组: ('albert-einstein',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/author.html
   ✅ 重写路径为: /author.html
未匹配的路径，可能需要添加新的URL模式: /authors/albert-einstein/
重定向到 author.html
[127.0.0.1] "GET /authors/albert-einstein/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/author.js?v=20250626
🔍 解析路径: /js/pages/author.js
❌ 未匹配到任何语义化URL模式: /js/pages/author.js
✅ 静态文件请求: /js/pages/author.js
[127.0.0.1] "GET /js/pages/author.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /authors/
🔍 解析路径: /authors/
✅ 匹配到语义化URL模式: ^/authors/$ -> authors.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/authors.html
   ✅ 重写路径为: /authors.html
未匹配的路径，可能需要添加新的URL模式: /authors/
重定向到 authors.html
[127.0.0.1] "GET /authors/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/performance-optimizations.css
🔍 解析路径: /css/performance-optimizations.css
❌ 未匹配到任何语义化URL模式: /css/performance-optimizations.css
✅ 静态文件请求: /css/performance-optimizations.css
[127.0.0.1] "GET /css/performance-optimizations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/performance-monitor.js?v=20250627
🔍 解析路径: /js/performance-monitor.js
❌ 未匹配到任何语义化URL模式: /js/performance-monitor.js
✅ 静态文件请求: /js/performance-monitor.js
[127.0.0.1] "GET /js/performance-monitor.js?v=20250627 HTTP/1.1" 200 -
🔍 收到GET请求: /js/config.js?v=20250626
🔍 解析路径: /js/config.js
❌ 未匹配到任何语义化URL模式: /js/config.js